import { useState, useEffect } from "react";
import {
  GoogleAuthProvider,
  GithubAuthProvider,
  signInWithPopup,
} from "firebase/auth";
import {
  setLogLevel,
  doc,
  getDoc,
  setDoc,
  serverTimestamp,
} from "firebase/firestore";
import { auth, db } from "../../firebase";
import Footer from "./Footer";
import InterviewGym from "./InterviewGym";
import { questionsData } from "../constants.js";

const FeedbackBanner = () => (
  <div className="w-full bg-gradient-to-r from-zen-accent/20 via-zen-accent/30 to-zen-accent/20 dark:from-zen-accent/10 dark:via-zen-accent/20 dark:to-zen-accent/10 py-3 px-4 animate-fade-in">
    <div className="max-w-7xl mx-auto flex flex-col sm:flex-row items-center justify-center gap-2 text-center sm:text-left">
      <p className="font-zen text-base text-zen-ink dark:text-zen-paper">
        🎯 We want to hear from you! Share your feedback and get free credits!
      </p>
      <a
        href="https://discord.gg/UtCPAc8sdc"
        target="_blank"
        rel="noopener noreferrer"
        className="font-zen text-base text-zen-accent hover:text-zen-accent/80 transition-colors flex items-center gap-1"
      >
        Join our Discord
        <svg
          className="w-4 h-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
          />
        </svg>
      </a>
    </div>
  </div>
);

export default function Login({ onLogin }) {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(""); // Empty string means not loading, otherwise contains provider name
  const [trialMode, setTrialMode] = useState(false); // Track if user is in trial mode

  // For trial, randomly select one medium question
  const getRandomTrialQuestion = () => {
    const mediumQuestions = questionsData.medium;
    const randomIndex = Math.floor(Math.random() * mediumQuestions.length);
    return mediumQuestions[randomIndex];
  };

  const [trialQuestion] = useState(() => getRandomTrialQuestion());
  const [trialComplete, setTrialComplete] = useState(false);
  const [trialFeedback, setTrialFeedback] = useState(null);

  useEffect(() => {
    setLogLevel("debug"); // enables detailed Firestore logs

    // Apply consistent viewport settings for both mobile and desktop
    document.body.style.overflow = "auto";
    document.body.style.height = "100%";
    document.body.style.margin = "0";
    document.body.style.padding = "0";
    document.body.style.touchAction = "pan-y";
    document.documentElement.style.height = "100%";
    document.documentElement.style.overflow = "auto";
    document.documentElement.style.margin = "0";
    document.documentElement.style.padding = "0";

    // Force layout recalculation
    setTimeout(() => {
      window.scrollTo(0, 1);
      window.scrollTo(0, 0);
    }, 100);

    return () => {
      // Clean up
      document.body.style.overflow = "";
      document.body.style.height = "";
      document.body.style.margin = "";
      document.body.style.padding = "";
      document.body.style.touchAction = "";
      document.documentElement.style.height = "";
      document.documentElement.style.overflow = "";
      document.documentElement.style.margin = "";
      document.documentElement.style.padding = "";
    };
  }, []);

  // Generic sign-in handler that works with any provider
  const handleSignIn = async (providerInstance, providerName) => {
    try {
      setLoading(providerName);
      setError("");

      const result = await signInWithPopup(auth, providerInstance);

      // Create or update user document
      const userDocRef = doc(db, "users", result.user.uid);
      const userDoc = await getDoc(userDocRef);

      if (!userDoc.exists()) {
        // If the user document doesn't exist, create it
        await setDoc(userDocRef, {
          email: result.user.email,
          displayName: result.user.displayName,
          photoURL: result.user.photoURL,
          provider: providerName, // Track which provider was used
          credits: 3, // Start with 3 credits
          createdAt: serverTimestamp(),
        });
      }

      onLogin(result.user);
    } catch (err) {
      console.error(`${providerName} sign-in failed`, err);
      if (err?.message?.includes("offline")) {
        setError(
          "Failed to sign in. Your browser may be blocking network requests or security rules denied access."
        );
      } else if (
        err?.code === "auth/account-exists-with-different-credential"
      ) {
        setError(
          "An account already exists with the same email address but different sign-in credentials. Try signing in using a provider associated with this email address."
        );
      } else {
        setError("Sign in failed. Please try again.");
      }
    } finally {
      setLoading("");
    }
  };

  // Provider-specific handlers
  const handleGoogleSignIn = () =>
    handleSignIn(new GoogleAuthProvider(), "google");
  const handleGithubSignIn = () =>
    handleSignIn(new GithubAuthProvider(), "github");

  // Split content into separate components to avoid possible rendering issues
  const HeroSection = () => (
    <div className="w-full flex flex-col items-center p-4 mt-4 animate-fade-in">
      <div className="relative">
        {/* Animated gradient border */}
        <div className="absolute -inset-1 bg-gradient-to-r from-zen-accent via-zen-accent/50 to-zen-accent rounded-lg blur opacity-30 animate-gradient-x"></div>

        <div className="relative">
          <h1 className="font-zen text-5xl md:text-7xl text-zen-ink dark:text-zen-paper tracking-[0.2em] relative z-10 animate-slide-down">
            <span className="text-zen-accent animate-pulse-slow">侍</span>{" "}
            SAMURAI
          </h1>
          <h2 className="font-zen text-2xl md:text-4xl text-zen-ink/80 dark:text-zen-paper/80 tracking-widest mt-2 text-center animate-slide-up">
            INTERVIEW
          </h2>
        </div>
      </div>
    </div>
  );

  const Divider = () => (
    <div className="w-full flex items-center justify-center my-12">
      <div className="h-px w-full max-w-3xl bg-gradient-to-r from-transparent via-zen-accent/50 to-transparent relative">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-zen-accent to-transparent opacity-50 animate-pulse-slow"></div>
      </div>
    </div>
  );

  const VideoDemoSection = () => (
    <div className="w-full max-w-4xl mx-auto p-4 mt-8 animate-fade-in">
      <div className="relative">
        {/* Animated gradient border */}
        <div className="absolute -inset-1 bg-gradient-to-r from-zen-accent via-zen-accent/50 to-zen-accent rounded-lg blur opacity-30 animate-gradient-x"></div>
        
        <div className="relative bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
          <div className="aspect-video w-full overflow-hidden rounded-zen">
            <iframe
              className="w-full h-full"
              src="https://www.youtube.com/embed/qFolgxggfuo"
              title="Samurai Interview Demo"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            ></iframe>
          </div>
        </div>
      </div>
    </div>
  );

  const FAQSection = () => (
    <div className="w-full max-w-4xl mx-auto p-4 mt-16 animate-fade-in">
      <div className="relative">
        {/* Animated gradient border */}
        <div className="absolute -inset-1 bg-gradient-to-r from-zen-accent via-zen-accent/50 to-zen-accent rounded-lg blur opacity-30 animate-gradient-x"></div>
        
        <div className="relative bg-zen-paper/50 dark:bg-zen-ink/50 p-6 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
          <h3 className="text-3xl md:text-4xl font-zen text-zen-ink dark:text-zen-paper mb-6 text-center animate-slide-down">
            Frequently Asked Questions
          </h3>
          
          <div className="space-y-6">
            <div className="animate-fade-in-up">
              <h4 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-2">
                Q: What is this website?
              </h4>
              <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                Samurai Interview helps job seekers practice and ace technical interviews. It simulates a real interview environment and provides actionable feedback to help you improve.
              </p>
            </div>

            <div className="animate-fade-in-up-delay-1">
              <h4 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-2">
                Q: What kind of interviews do you support?
              </h4>
              <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                Right now, we focus on technical coding interviews for software engineers. Soon, we'll expand to include front-end, back-end, behavioral interviews, and more.
              </p>
            </div>

            <div className="animate-fade-in-up-delay-2">
              <h4 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-2">
                Q: Do I need to pay for this?
              </h4>
              <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                You get 3 interviews for free. Additional interviews can be purchased at a nominal cost.
              </p>
            </div>

            <div className="animate-fade-in-up-delay-3">
              <h4 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-2">
                Q: How should I interpret the feedback?
              </h4>
              <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                You'll receive a score out of 100 across various categories. A score of 85+ generally indicates you're performing at a level that would pass real interviews. Our goal is to help you land the job—not keep you coming back.
              </p>
            </div>

            <div className="animate-fade-in-up-delay-3">
              <h4 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-2">
                Q: How can I provide feedback to the product?
              </h4>
              <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                We're an early-stage product and would love your input. Join us on{" "}
                <a
                  href="https://discord.gg/UtCPAc8sdc"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-zen-accent hover:text-zen-accent/80 transition-colors underline"
                >
                  Discord
                </a>{" "}
                to share feedback, ideas, or just say hello.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const FeaturesSection = () => (
    <div className="space-y-6 p-4">
      <div className="space-y-4">
        <h3 className="text-3xl md:text-5xl font-zen text-zen-ink dark:text-zen-paper animate-slide-right">
          Mock Interviews for Technical Roles
        </h3>
        <p className="text-xl md:text-2xl text-zen-ink/70 dark:text-zen-paper/70 font-zen animate-slide-right-delay">
          Master your interview skills
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Feature Cards */}
        <div className="bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-zen-accent/10 rounded-lg animate-pulse-slow">
              <svg
                className="w-5 h-5 text-zen-accent"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                />
              </svg>
            </div>
            <div>
              <h4 className="text-lg md:text-xl font-zen text-zen-ink dark:text-zen-paper">
                Dynamic Mock Interviews
              </h4>
              <p className="text-base text-zen-ink/60 dark:text-zen-paper/60 mt-1">
                Experience interviews that simulate the real thing
              </p>
            </div>
          </div>
        </div>

        <div className="bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-zen-accent/10 rounded-lg animate-pulse-slow">
              <svg
                className="w-5 h-5 text-zen-accent"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
            </div>
            <div>
              <h4 className="text-lg md:text-xl font-zen text-zen-ink dark:text-zen-paper">
                Real-time Feedback
              </h4>
              <p className="text-base text-zen-ink/60 dark:text-zen-paper/60 mt-1">
                Get instant analysis, feedback, and suggestions
              </p>
            </div>
          </div>
        </div>

        <div className="bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-zen-accent/10 rounded-lg animate-pulse-slow">
              <svg
                className="w-5 h-5 text-zen-accent"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
            <div>
              <h4 className="text-lg md:text-xl font-zen text-zen-ink dark:text-zen-paper">
                Industry Questions & Learning Paths(Coming Soon!)
              </h4>
              <p className="text-base text-zen-ink/60 dark:text-zen-paper/60 mt-1">
                Tailored questions and personalized learning paths
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const SuccessText = () => (
    <div className="mt-8 p-6 bg-zen-accent/5 dark:bg-zen-accent/10 rounded-zen border border-zen-accent/20 backdrop-blur-sm animate-fade-in">
      <div className="flex items-center justify-center gap-3">
        <svg className="w-6 h-6 text-zen-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
        </svg>
        <p className="text-xl font-zen text-zen-ink dark:text-zen-paper text-center">
          Our users have landed roles at top companies, including FAANG and other elite startups.
        </p>
      </div>
    </div>
  );

  const LoginSection = () => (
    <div className="bg-zen-paper/80 dark:bg-zen-ink/80 p-4 rounded-zen shadow-zen w-full max-w-md border border-zen-accent/20 backdrop-blur-sm relative overflow-hidden">
      {/* Animated gradient border */}
      <div className="absolute -inset-1 bg-gradient-to-r from-zen-accent via-zen-accent/50 to-zen-accent rounded-lg blur opacity-20 animate-gradient-x"></div>

      <div className="relative">
        <div className="text-center mb-6">
          <h3 className="font-zen text-2xl md:text-3xl text-zen-ink dark:text-zen-paper mb-2">
            Begin Your Journey
          </h3>
          <p className="font-zen text-base text-zen-ink/70 dark:text-zen-paper/70">
            Sign in to enter the dojo
          </p>
        </div>

        {/* Sexy Try Free Button */}
        <button
          onClick={() => setTrialMode(true)}
          className="w-full flex items-center justify-center gap-3 p-4 mb-6 bg-gradient-to-r from-green-900 via-green-700 to-black text-white font-zen text-lg md:text-xl rounded-zen shadow-2xl border-2 border-green-500 hover:scale-105 active:scale-95 transition-all duration-200 animate-pulse-slow focus:outline-none focus:ring-4 focus:ring-green-400/40 relative overflow-visible"
          style={{ boxShadow: '0 6px 32px 0 rgba(34,197,94,0.25), 0 0 16px 4px #22c55e' }}
        >
          {/* Animated Green Sparks */}
          <span className="absolute -top-3 -left-3 animate-sparkle">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="2" fill="#22c55e" fillOpacity="0.8">
                <animate attributeName="r" values="2;4;2" dur="1.2s" repeatCount="indefinite" />
                <animate attributeName="opacity" values="1;0.5;1" dur="1.2s" repeatCount="indefinite" />
              </circle>
            </svg>
          </span>
          <span className="absolute -bottom-2 left-1/2 -translate-x-1/2 animate-sparkle-delay">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="1.5" fill="#4ade80" fillOpacity="0.7">
                <animate attributeName="r" values="1.5;3;1.5" dur="1.5s" repeatCount="indefinite" />
                <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite" />
              </circle>
            </svg>
          </span>
          <span className="absolute -top-2 right-2 animate-sparkle-delay-2">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
              <circle cx="10" cy="10" r="1" fill="#bbf7d0" fillOpacity="0.6">
                <animate attributeName="r" values="1;2;1" dur="1.1s" repeatCount="indefinite" />
                <animate attributeName="opacity" values="1;0.2;1" dur="1.1s" repeatCount="indefinite" />
              </circle>
            </svg>
          </span>
          <svg className="w-6 h-6 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M12 20a8 8 0 100-16 8 8 0 000 16z" />
          </svg>
          Try a Free Interview - No Login Required
        </button>

        {/* Login Buttons */}
        <div className="space-y-4">
          <button
            onClick={handleGoogleSignIn}
            disabled={!!loading}
            className="relative w-full flex items-center justify-center gap-3 p-3 bg-zen-paper dark:bg-zen-ink border border-zen-accent/30 rounded-zen text-zen-ink dark:text-zen-paper font-zen active:bg-zen-accent/10 dark:active:bg-zen-accent/20 transition-colors touch-manipulation"
          >
            {loading === "google" ? (
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 border-2 border-zen-accent/50 border-t-zen-accent rounded-full animate-spin mr-2"></span>
                <span>Loading...</span>
              </div>
            ) : (
              <>
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/c/c1/Google_%22G%22_logo.svg"
                  alt="Google Logo"
                  className="w-5 h-5"
                />
                <span>Sign in with Google</span>
              </>
            )}
          </button>

          <button
            onClick={handleGithubSignIn}
            disabled={!!loading}
            className="relative w-full flex items-center justify-center gap-3 p-3 bg-zen-paper dark:bg-zen-ink border border-zen-accent/30 rounded-zen text-zen-ink dark:text-zen-paper font-zen active:bg-zen-accent/10 dark:active:bg-zen-accent/20 transition-colors touch-manipulation"
          >
            {loading === "github" ? (
              <div className="flex items-center">
                <span className="inline-block w-4 h-4 border-2 border-zen-accent/50 border-t-zen-accent rounded-full animate-spin mr-2"></span>
                <span>Loading...</span>
              </div>
            ) : (
              <>
                <svg
                  viewBox="0 0 24 24"
                  className="w-5 h-5"
                  fill="currentColor"
                >
                  <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.237 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                </svg>
                <span>Sign in with GitHub</span>
              </>
            )}
          </button>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-zen-red/10 border border-zen-red/30 rounded-zen text-zen-red text-base font-zen">
            <div className="flex items-center">
              <svg
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5 mr-2 text-zen-red"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="9"
                  stroke="currentColor"
                  strokeWidth="2"
                />
                <path
                  d="M12 7V13"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <circle cx="12" cy="16" r="1" fill="currentColor" />
              </svg>
              {error}
            </div>
          </div>
        )}

        <div className="mt-6 text-center">
          <p className="font-zen text-sm text-zen-ink/50 dark:text-zen-paper/50">
            Prepare yourself for the path of mastery
          </p>
          <p className="font-zen text-sm text-zen-accent mt-1">
            Try for free - no credit card required
          </p>
        </div>
      </div>
    </div>
  );

  // If in trial mode, render only the trial interface
  if (trialMode) {
    return (
      <div className="min-h-screen bg-zen-pattern dark:bg-zen-pattern">
        {!trialComplete ? (
          <InterviewGym
            user={null}
            initialTab="interview"
            trialMode={true}
            presetQuestion={trialQuestion}
            onTrialComplete={(feedback) => {
              setTrialFeedback(feedback);
              setTrialComplete(true);
            }}
          />
        ) : (
          <div className="min-h-screen flex items-center justify-center p-4 bg-zen-pattern dark:bg-zen-pattern">
            <div className="bg-green-900/80 p-8 rounded-zen border-2 border-green-500 text-center animate-fade-in max-w-md w-full">
              <h2 className="font-zen text-2xl md:text-3xl text-green-400 mb-4">Trial Complete!</h2>
              <p className="font-zen text-lg text-white mb-6">Want more questions and custom interviews?</p>
              <button
                onClick={() => {
                  setTrialMode(false);
                  setTrialComplete(false);
                  setTrialFeedback(null);
                }}
                className="px-6 py-3 bg-green-500 text-white rounded-zen font-zen text-lg hover:bg-green-400 transition mb-4"
              >
                Create an Account for More
              </button>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Apply CSS to enable consistent scrolling and viewport handling */}
      <style>{`
        html,
        body {
          height: 100% !important;
          margin: 0 !important;
          padding: 0 !important;
          overflow-y: auto !important;
          -webkit-overflow-scrolling: touch !important;
          overscroll-behavior-y: auto !important;
          touch-action: pan-y !important;
        }
        body {
          padding-right: 0 !important; /* Prevent layout shift */
        }
        #__next {
          min-height: 100vh !important;
          display: flex;
          flex-direction: column;
          /* Set min-height to ensure consistent spacing */
          min-height: 100vh;
        }
        .main-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          /* Set min-height to ensure consistent spacing */
          min-height: 100vh;
        }
        @keyframes gradient-x {
          0%,
          100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }
        @keyframes shake {
          0%,
          100% {
            transform: translateX(0);
          }
          25% {
            transform: translateX(-5px);
          }
          75% {
            transform: translateX(5px);
          }
        }
        .animate-gradient-x {
          animation: gradient-x 15s ease infinite;
          background-size: 200% 200%;
        }
        .animate-shake {
          animation: shake 0.5s ease-in-out;
        }
        .animate-pulse-slow {
          animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .animate-slide-down {
          animation: slideDown 0.5s ease-out;
        }
        .animate-slide-up {
          animation: slideUp 0.5s ease-out;
        }
        .animate-slide-right {
          animation: slideRight 0.5s ease-out;
        }
        .animate-slide-right-delay {
          animation: slideRight 0.5s ease-out 0.2s;
          animation-fill-mode: both;
        }
        .animate-fade-in {
          animation: fadeIn 0.5s ease-out;
        }
        .animate-fade-in-up {
          animation: fadeInUp 0.5s ease-out;
        }
        .animate-fade-in-up-delay-1 {
          animation: fadeInUp 0.5s ease-out 0.2s;
          animation-fill-mode: both;
        }
        .animate-fade-in-up-delay-2 {
          animation: fadeInUp 0.5s ease-out 0.4s;
          animation-fill-mode: both;
        }
        .animate-fade-in-up-delay-3 {
          animation: fadeInUp 0.5s ease-out 0.6s;
          animation-fill-mode: both;
        }
        @keyframes slideDown {
          from {
            transform: translateY(-20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        @keyframes slideUp {
          from {
            transform: translateY(20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        @keyframes slideRight {
          from {
            transform: translateX(-20px);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        @keyframes fadeInUp {
          from {
            transform: translateY(20px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        .animate-sparkle {
          animation: sparkle 1.2s linear infinite;
        }
        .animate-sparkle-delay {
          animation: sparkle 1.5s linear infinite 0.5s;
        }
        .animate-sparkle-delay-2 {
          animation: sparkle 1.1s linear infinite 0.8s;
        }
        @keyframes sparkle {
          0%, 100% {
            opacity: 1;
            filter: drop-shadow(0 0 6px #22c55e) drop-shadow(0 0 12px #4ade80);
          }
          50% {
            opacity: 0.5;
            filter: drop-shadow(0 0 12px #4ade80) drop-shadow(0 0 24px #bbf7d0);
          }
        }
      `}</style>

      {/* Container with controlled height and padding */}
      <div className="main-container w-full bg-gradient-to-br from-zen-paper via-zen-paper/95 to-zen-paper/90 dark:from-zen-ink dark:via-zen-ink/95 dark:to-zen-ink/90">
        <FeedbackBanner />
        <div className="flex-grow py-8 md:py-12">
          <div className="w-full max-w-7xl mx-auto px-4">
            <HeroSection />
            <Divider />

            <div className="mt-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center justify-items-center">
                <div className="w-full max-w-xl">
                  <FeaturesSection />
                </div>
                <div className="w-full max-w-md">
                  <LoginSection />
                </div>
              </div>
            </div>

            <Divider />

            {/* Video Demo at bottom */}
            <div className="mt-16">
              <VideoDemoSection />
            </div>

            <Divider />

            <div className="mt-8">
              <FAQSection />
            </div>

            <div className="mt-8">
              <SuccessText />
            </div>
          </div>
        </div>
        <Footer />
      </div>
    </>
  );
}
