import { useEffect, useRef, useState } from 'react'
import AceEditor from 'react-ace'

// Import modes (languages)
import 'ace-builds/src-noconflict/mode-javascript'
import 'ace-builds/src-noconflict/mode-typescript'
import 'ace-builds/src-noconflict/mode-python'
import 'ace-builds/src-noconflict/mode-java'
import 'ace-builds/src-noconflict/mode-golang'
import 'ace-builds/src-noconflict/mode-ruby'
import 'ace-builds/src-noconflict/mode-c_cpp'
import 'ace-builds/src-noconflict/mode-csharp'
import 'ace-builds/src-noconflict/mode-rust'
import 'ace-builds/src-noconflict/mode-swift'

// Import themes
import 'ace-builds/src-noconflict/theme-github'
import 'ace-builds/src-noconflict/theme-monokai'

// Import basic language tools without snippets to avoid the error
import 'ace-builds/src-noconflict/ext-searchbox'
import { onSnapshot, getFirestore, doc, updateDoc, increment } from 'firebase/firestore'
import SessionControls from './SessionControls'
import ToolPanel from './ToolPanel'
import ProfileTab from './ProfileTab'
import ConfirmationModal from './ConfirmationModal'
import NavigationBar from './NavigationBar'
import FeedbackDisplay from './FeedbackDisplay'
import CreditModal from './CreditModal'
import { getAceEditorOptions } from '../utils/styling'
import { codeTemplates, getEditorMode, getEditorTheme } from '../utils/codeTemplates'
import { startCombinedRecording, stopCombinedRecording, cleanupAudioResources } from '../utils/audioRecording'
import RoleSelection from './RoleSelection'
import { trackEvent } from '../utils/analytics'

export default function InterviewGym({ user, initialTab = 'interview', trialMode = false, presetQuestion = null, onTrialComplete }) {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [showCreditModal, setShowCreditModal] = useState(false)
  const [userCredits, setUserCredits] = useState(0)

  const [isSessionActive, setIsSessionActive] = useState(false)
  const [isGatheringFeedback, setIsGatheringFeedback] = useState(false)
  const [events, setEvents] = useState([])
  const [dataChannel, setDataChannel] = useState(null)
  const [selectedLanguage, setSelectedLanguage] = useState('javascript')
  const [code, setCode] = useState(codeTemplates[selectedLanguage])
  const [darkMode, setDarkMode] = useState(() => {
    // Initialize from localStorage if available, otherwise default to false
    const savedMode = localStorage.getItem('darkMode')
    return savedMode !== null ? JSON.parse(savedMode) : false
  })
  const [sessionEndTime, setSessionEndTime] = useState(null)
  const [timeLeft, setTimeLeft] = useState(0)
  const [remoteStream, setRemoteStream] = useState(null)
  const [micStream, setMicStream] = useState(null)
  const [currSessionFeedback, setCurrSessionFeedback] = useState(null)
  const [audioBlob, setAudioBlob] = useState(null)
  const [recordingError, setRecordingError] = useState(null)
  const [currentQuestion, setCurrentQuestion] = useState(trialMode && presetQuestion ? presetQuestion : null)
  const [showNavigationModal, setShowNavigationModal] = useState(false)
  const [pendingTabChange, setPendingTabChange] = useState(null)
  const [hasMicPermission, setHasMicPermission] = useState(false)
  const [selectedRole, setSelectedRole] = useState(trialMode ? 'software_engineer' : null)
  const [showMicPrompt, setShowMicPrompt] = useState(false)

  const peerConnection = useRef(null)
  const audioElement = useRef(null)
  const editorContainerRef = useRef(null)
  const timerRef = useRef(null)
  const mediaRecorderRef = useRef(null)
  const audioChunksRef = useRef([])
  const audioContextRef = useRef(null)
  const db = getFirestore()

  const roles = [
    {
      id: 'software_engineer',
      title: 'Software Engineer',
      description: 'Master algorithms, data structures, and system design',
      enabled: true
    },
    {
      id: 'frontend_engineer',
      title: 'Frontend Engineer',
      description: 'Specialize in UI/UX, React, and modern web technologies',
      enabled: false
    },
    {
      id: 'backend_engineer',
      title: 'Backend Engineer',
      description: 'Focus on server-side development, databases, and APIs',
      enabled: false
    },
    {
      id: 'product_manager',
      title: 'Product Manager',
      description: 'Lead product strategy, user research, and feature prioritization',
      enabled: false
    },
    {
      id: 'data_scientist',
      title: 'Data Scientist',
      description: 'Work with machine learning, statistics, and data analysis',
      enabled: false
    }
  ];

  useEffect(() => {
    if (isSessionActive && sessionEndTime) {
      const id = setInterval(() => {
        const now = Date.now()
        const remaining = sessionEndTime - now
        if (remaining <= 0) stopSession()
        else setTimeLeft(Math.floor(remaining / 1000))
      }, 1000)
      return () => clearInterval(id)
    }
  }, [isSessionActive, sessionEndTime])

  useEffect(() => {
    if (editorContainerRef.current) {
      darkMode
        ? document.documentElement.classList.add('dark-theme')
        : document.documentElement.classList.remove('dark-theme')

      // Save preference to localStorage
      localStorage.setItem('darkMode', JSON.stringify(darkMode))
    }
  }, [darkMode])

  // Track language changes
  useEffect(() => {
    // Only update if the code is empty or matches a template (to avoid overwriting user's code)
    const currentTemplate = codeTemplates[selectedLanguage]
    const isDefaultTemplate = Object.values(codeTemplates).some(template => code === template)

    if (!code || code === '// Write your code here' || isDefaultTemplate) {
      setCode(currentTemplate)
      // Track language change
      trackEvent('change_language', 'editor', selectedLanguage)
    }
  }, [selectedLanguage])

  // Track theme changes
  useEffect(() => {
    if (editorContainerRef.current) {
      darkMode
        ? document.documentElement.classList.add('dark-theme')
        : document.documentElement.classList.remove('dark-theme')

      // Save preference to localStorage
      localStorage.setItem('darkMode', JSON.stringify(darkMode))
      
      // Track theme change
      trackEvent('change_theme', 'preferences', darkMode ? 'dark' : 'light')
    }
  }, [darkMode])

  // Add browser-level protection for when users try to leave the page during active session
  useEffect(() => {
    // Handle tab/browser close
    const handleBeforeUnload = (e) => {
      if (isSessionActive) {
        // Standard way to show a confirmation dialog when leaving the page
        e.preventDefault()
        // Chrome requires returnValue to be set
        e.returnValue = 'Your training session is still active. If you leave now, your progress will be lost.'
        // Return a string for older browsers
        return 'Your training session is still active. If you leave now, your progress will be lost.'
      }
    }

    // Save current location to compare against future navigation attempts
    const currentPath = window.location.pathname

    // Handle navigation within the same tab
    const handlePopState = (e) => {
      if (isSessionActive) {
        // If user tries to navigate away, prevent it and show our custom modal
        e.preventDefault()
        setShowNavigationModal(true)
        // Push the current path back to the history to stay on the page
        window.history.pushState(null, '', currentPath)
      }
    }

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('popstate', handlePopState)

    // Push initial state to enable popstate detection
    if (isSessionActive) {
      window.history.pushState(null, '', currentPath)
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handlePopState)
    }
  }, [isSessionActive])

  useEffect(() => {
    // Start recording when both streams are available
    if (remoteStream && micStream && !mediaRecorderRef.current) {
      const refs = {
        mediaRecorderRef,
        audioChunksRef,
        audioContextRef
      }
      startCombinedRecording(remoteStream, micStream, refs, setRecordingError)
    }
  }, [remoteStream, micStream])

  // Add mic permission check
  useEffect(() => {
    const checkMicPermission = async () => {
      try {
        const result = await navigator.permissions.query({ name: 'microphone' })
        setHasMicPermission(result.state === 'granted')
        
        if (result.state === 'prompt') {
          setShowMicPrompt(true)
        }
        
        result.addEventListener('change', () => {
          setHasMicPermission(result.state === 'granted')
          setShowMicPrompt(result.state === 'prompt')
        })
      } catch (error) {
        console.error('Error checking mic permission:', error)
        setShowMicPrompt(true)
      }
    }
    
    checkMicPermission()
  }, [])

  const handleRequestMicPermission = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      stream.getTracks().forEach(track => track.stop())
      setHasMicPermission(true)
      setShowMicPrompt(false)
    } catch (error) {
      console.error('Error requesting mic permission:', error)
      setShowMicPrompt(true)
    }
  }

  // Track session start
  async function startSession() {
    if (!trialMode) {
      // Check if user has enough credits
      if (userCredits <= 0) {
        alert("You don't have enough credits to start an interview. Please purchase more credits.");
        return;
      }
      // Track session start event
      trackEvent('start_session', 'interview', selectedRole, userCredits)
      // Reset any previous recording error
      setRecordingError(null)
      // Deduct one credit
      try {
        await updateDoc(doc(db, "users", user.uid), {
          credits: increment(-1)
        });
      } catch (error) {
        console.error("Error deducting credit:", error);
        alert("Failed to start session. Please try again.");
        return;
      }
    } else {
      // In trial mode, just track event and allow session
      trackEvent('start_session_trial', 'interview', selectedRole, 1)
      setRecordingError(null)
    }

    const tokenResponse = await fetch('/token')
    const data = await tokenResponse.json()
    const EPHEMERAL_KEY = data.client_secret.value
    const pc = new RTCPeerConnection()
    audioElement.current = document.createElement('audio')
    audioElement.current.autoplay = true
    audioElement.current.muted = false

    pc.ontrack = e => {
      audioElement.current.srcObject = e.streams[0]
      setRemoteStream(e.streams[0])
      e.streams[0].getAudioTracks().forEach(track => track.enabled = true)
    }

    const ms = await navigator.mediaDevices.getUserMedia({ audio: true })
    pc.addTrack(ms.getTracks()[0])
    setMicStream(ms)

    const dc = pc.createDataChannel('oai-events')
    setDataChannel(dc)
    dc.onopen = () => {
      setIsSessionActive(true)
    }

    const offer = await pc.createOffer()
    await pc.setLocalDescription(offer)
    const baseUrl = 'https://api.openai.com/v1/realtime'
    const model = 'gpt-4o-realtime-preview-2025-06-03'
    const sdpResponse = await fetch(`${baseUrl}?model=${model}`, {
      method: 'POST',
      body: offer.sdp,
      headers: {
        Authorization: `Bearer ${EPHEMERAL_KEY}`,
        'Content-Type': 'application/sdp'
      }
    })
    const answer = { type: 'answer', sdp: await sdpResponse.text() }
    await pc.setRemoteDescription(answer)
    peerConnection.current = pc
    const endTime = Date.now() + 30 * 60 * 1000
    setSessionEndTime(endTime)
    timerRef.current = setTimeout(() => stopSession(), 30 * 60 * 1000)
  }

  // Track session end
  async function stopSession() {
    // Track session end event
    trackEvent('end_session', 'interview', selectedRole, timeLeft)

    // Immediately mute the audio to stop playback
    if (audioElement.current) audioElement.current.muted = true
    setIsGatheringFeedback(true)

    let recordedBlob = null
    try {
      // Stop recording and get the blob using utility function
      const refs = {
        mediaRecorderRef,
        audioChunksRef,
        audioContextRef
      }
      recordedBlob = await stopCombinedRecording(refs)

      if (recordedBlob) {
        setAudioBlob(recordedBlob)

        // Check if the file is too large
        if (recordedBlob.size > 25 * 1024 * 1024) {
          setRecordingError('Recording exceeded 25MB. Transcription may fail.')
        }

        const mimeType = mediaRecorderRef.current ? mediaRecorderRef.current.mimeType : 'audio/webm'
        let extension = 'webm'
        if (mimeType.includes('mp4')) extension = 'm4a'
        else if (mimeType.includes('ogg')) extension = 'ogg'

        // Send for transcription with proper headers
        const formData = new FormData()
        formData.append('code', code)
        formData.append('audio', recordedBlob, `recorded_audio.${extension}`)
        formData.append('format', extension)

        try {
          const response = await fetch('/transcribe', {
            method: 'POST',
            body: formData
          })

          // Always try to get a response, even if there's an error
          try {
            const result = await response.json()

            if (response.ok) {
              setCurrSessionFeedback(result)
            } else {
              // If we got a structured error response with choices, use it
              if (result.choices && result.choices[0]?.message?.content) {
                setCurrSessionFeedback(result)
                setRecordingError(`API Error: ${result.choices[0].message.content.score_justification || 'Unknown error'}`)
              } else {
                // Otherwise create a fallback response
                setCurrSessionFeedback({
                  choices: [{
                    message: {
                      content: {
                        score: 0,
                        score_justification: `Transcription failed: ${result.error || result.details || 'Unknown error'}`,
                        feedback: "The system encountered an error. Please try again.",
                        sub_scores: {}
                      }
                    }
                  }]
                })
                setRecordingError(`Transcription error: ${result.error || result.details || 'Unknown error'}`)
              }
            }
          } catch (parseError) {
            setCurrSessionFeedback({
              choices: [{
                message: {
                  content: {
                    score: 0,
                    score_justification: `Failed to parse response: ${parseError.message}`,
                    feedback: "The system encountered an error. Please try again.",
                    sub_scores: {}
                  }
                }
              }]
            })
            setRecordingError(`Response parsing error: ${parseError.message}`)
          }
        } catch (error) {
          setCurrSessionFeedback({
            score: 0,
            score_justification: `Transcription error: ${error.message}`,
            feedback: ''
          })
        }
      } else {
        setCurrSessionFeedback({
          score: 0,
          score_justification: 'No recording available.',
          feedback: ''
        })
      }
    } catch (error) {
      setRecordingError(`Error stopping recording: ${error.message}`)
      setCurrSessionFeedback({
        score: 0,
        score_justification: `Recording error: ${error.message}`,
        feedback: ''
      })
    } finally {
      // Clean up audio resources using utility function
      cleanupAudioResources(remoteStream, micStream, {
        mediaRecorderRef,
        audioContextRef
      })

      if (timerRef.current) {
        clearTimeout(timerRef.current)
        timerRef.current = null
      }

      peerConnection.current = null
      setIsSessionActive(false)
      setSessionEndTime(null)
      setTimeLeft(0)
      setIsGatheringFeedback(false)
    }
  }

  function sendClientEvent(message) {
    if (dataChannel && dataChannel.readyState === 'open') {
      const timestamp = new Date().toLocaleTimeString()
      message.event_id = message.event_id || crypto.randomUUID()
      dataChannel.send(JSON.stringify(message))
      if (!message.timestamp) message.timestamp = timestamp
      setEvents(prev => [message, ...prev])
    } else {
      console.error('Failed to send message - no data channel available', message)
    }
  }

  function sendTextMessage(message) {
    const event = {
      type: 'conversation.item.create',
      item: { type: 'message', role: 'user', content: [{ type: 'input_text', text: message }] }
    }
    sendClientEvent(event)
  }

  const handleResetSession = () => {
    setCurrSessionFeedback(null)
    setIsGatheringFeedback(false)
    setEvents([])
    setIsSessionActive(false)
    setDataChannel(null)
    setRemoteStream(null)
    setMicStream(null)
    setSessionEndTime(null)
    setTimeLeft(0)
    setAudioBlob(null)
    setRecordingError(null)

    // Clean up audio resources
    cleanupAudioResources(null, null, {
      mediaRecorderRef,
      audioContextRef
    })

    // Reset audio chunks
    audioChunksRef.current = []
  }

  // Track tab changes
  const handleTabChange = (tab) => {
    // If no active session or already on that tab, change immediately
    if (!isSessionActive || tab === activeTab) {
      setActiveTab(tab)
      // Track tab change
      trackEvent('change_tab', 'navigation', tab)
      return
    }

    // Otherwise, store the pending tab change and show confirmation modal
    setPendingTabChange(tab)
    setShowNavigationModal(true)
  }

  const handleConfirmNavigation = () => {
    // Proceed with tab change
    if (pendingTabChange) {
      setActiveTab(pendingTabChange)
      setPendingTabChange(null)
    }
    setShowNavigationModal(false)
  }

  const handleCancelNavigation = () => {
    // Cancel the navigation
    setPendingTabChange(null)
    setShowNavigationModal(false)
  }

  useEffect(() => {
    // Subscribe to user document for credits updates
    if (user?.uid) {
      const unsubscribe = onSnapshot(doc(db, "users", user.uid), (doc) => {
        if (doc.exists()) {
          setUserCredits(doc.data().credits || 0);
        }
      });
      return () => unsubscribe();
    }
  }, [user]);

  // In trial mode, skip credit check and set the question
  useEffect(() => {
    if (trialMode && presetQuestion) {
      setCurrentQuestion(presetQuestion);
      setUserCredits(1); // allow session to start
    }
  }, [trialMode, presetQuestion]);

  if (isGatheringFeedback && !currSessionFeedback)
    return (
      <div className="gathering-feedback-screen flex flex-col justify-center items-center h-screen text-center p-4 md:p-8 bg-zen-pattern dark:bg-samurai-pattern">
        <div className="bg-zen-paper dark:bg-zen-ink p-4 md:p-8 rounded-zen shadow-zen border border-zen-accent/20 w-full max-w-md">
          <div className="flex justify-center mb-6">
            <svg className="animate-spin text-zen-accent" width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h2 className="text-xl md:text-2xl font-zen text-zen-ink dark:text-zen-paper mb-4">Reflecting on Your Journey</h2>
          <p className="text-base md:text-lg font-zen text-zen-ink dark:text-zen-paper">The sensei is contemplating your performance...</p>
          <p className="text-sm mt-4 text-zen-ink/70 dark:text-zen-paper/70 font-zen break-words">
            Preparing the scroll of wisdom
            {audioBlob ? ` (${(audioBlob.size / (1024 * 1024)).toFixed(2)} MB)` : ''}
          </p>
          {recordingError && (
            <p className="text-sm text-zen-red mt-4 font-zen break-words">{recordingError}</p>
          )}
        </div>
      </div>
    )

  // In trial mode, call onTrialComplete after feedback is shown
  if (currSessionFeedback) {
    if (trialMode && onTrialComplete) {
      setTimeout(() => {
        onTrialComplete(currSessionFeedback);
      }, 500); // Give a short delay for UI to update
      return (
        <FeedbackDisplay
          feedback={currSessionFeedback}
          onReset={() => {}}
          recordingError={recordingError}
          user={user}
          currentQuestion={currentQuestion}
          handleTabChange={() => {}}
        />
      );
    }
    return <FeedbackDisplay
      feedback={currSessionFeedback}
      onReset={handleResetSession}
      recordingError={recordingError}
      user={user}
      currentQuestion={currentQuestion}
      handleTabChange={handleTabChange}
    />
  }

  // Show mic permission prompt if needed
  if (showMicPrompt) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-zen-pattern dark:bg-samurai-pattern p-4">
        <div className="max-w-md w-full bg-zen-paper dark:bg-zen-ink p-8 rounded-zen shadow-zen border border-zen-accent/20">
          <h2 className="text-2xl font-zen text-zen-ink dark:text-zen-paper mb-4">Microphone Access Required</h2>
          <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen mb-6">
            To begin your training, we need access to your microphone. This allows you to communicate with your sensei during the interview.
          </p>
          <button
            onClick={handleRequestMicPermission}
            className="w-full bg-zen-accent hover:bg-zen-accent/90 text-zen-paper font-zen py-3 px-6 rounded-zen transition-all"
          >
            Grant Microphone Access
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Hide NavigationBar and any background header in trial mode */}
      {!trialMode && (
        <NavigationBar
          activeTab={activeTab}
          onTabChange={handleTabChange}
          isSessionActive={isSessionActive}
          timeLeft={timeLeft}
          userCredits={userCredits}
          onCreditModalOpen={() => setShowCreditModal(true)}
        />
      )}
      {/* Feedback Button */}
      <a
        href="https://discord.gg/UtCPAc8sdc"
        target="_blank"
        rel="noopener noreferrer"
        className="fixed bottom-4 right-4 bg-zen-accent/80 hover:bg-zen-accent text-zen-paper text-xs px-2 py-1 rounded-zen shadow-zen transition-all duration-300 z-50"
      >
        Feedback/Contact
      </a>
      {/* Copyright Text */}
      <div className="fixed bottom-4 left-4 text-xs text-zen-ink/60 dark:text-zen-paper/60 font-zen z-50">
        © {new Date().getFullYear()} Samurai Interview. All rights reserved.
      </div>
      {/* Main Content Area */}
      {activeTab === 'interview' ? (
        <main className="absolute top-16 left-0 right-0 bottom-0 bg-zen-pattern dark:bg-samurai-pattern">
          {(!selectedRole && !trialMode) ? (
            <RoleSelection onSelect={setSelectedRole} />
          ) : (
            <div className="flex flex-col md:flex-row h-full">
              {/* Main editor section - responsive layout */}
              <section className="flex flex-col w-full md:w-[calc(100%-380px)] h-full md:h-auto overflow-hidden">
                <div className="p-4 flex flex-wrap gap-3 justify-between items-center bg-zen-paper/80 dark:bg-zen-ink/80 backdrop-blur-sm">
                  <div className="flex items-center gap-3">
                    <select
                      value={selectedLanguage}
                      onChange={e => setSelectedLanguage(e.target.value)}
                      className="bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent/50 rounded-zen px-3 py-2 font-zen min-w-[180px] transition-all hover:border-zen-accent focus:border-zen-accent focus:outline-none"
                    >
                      <optgroup label="Popular">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="go">Go</option>
                      </optgroup>
                      <optgroup label="More Languages">
                        <option value="typescript">TypeScript</option>
                        <option value="cpp">C++</option>
                        <option value="csharp">C#</option>
                        <option value="ruby">Ruby</option>
                        <option value="rust">Rust</option>
                        <option value="swift">Swift</option>
                      </optgroup>
                    </select>
                    <button
                      onClick={() => setSelectedRole(null)}
                      disabled={isSessionActive}
                      className={`relative group flex items-center gap-2 px-4 py-2 rounded-zen transition-all ${
                        isSessionActive
                          ? 'bg-zen-accent/5 text-zen-ink/30 dark:text-zen-paper/30 cursor-not-allowed'
                          : 'bg-zen-accent/10 hover:bg-zen-accent/20 text-zen-accent dark:text-zen-purple hover:shadow-lg hover:shadow-zen-accent/20'
                      }`}
                    >
                      <div className="flex items-center gap-2">
                        <svg 
                          className={`w-4 h-4 transition-transform ${!isSessionActive && 'group-hover:rotate-180'}`}
                          fill="none" 
                          stroke="currentColor" 
                          viewBox="0 0 24 24"
                        >
                          <path 
                            strokeLinecap="round" 
                            strokeLinejoin="round" 
                            strokeWidth={2} 
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" 
                          />
                        </svg>
                        <span className="font-zen">
                          {selectedRole === 'software_engineer' ? 'Software Engineer' : selectedRole}
                        </span>
                      </div>
                      {isSessionActive && (
                        <span className="absolute -top-2 -right-2 bg-zen-red text-zen-paper text-xs px-2 py-0.5 rounded-full">
                          Active
                        </span>
                      )}
                    </button>
                  </div>
                  <div className="hidden md:block max-w-l overflow-hidden text-ellipsis">
                    {!isSessionActive && <h3 className="font-zen text-zen-ink dark:text-zen-paper text-sm">Samurai mastered both sword and poetry, living by the pen as fiercely as the blade.</h3>}
                    {isSessionActive && <h3 className="font-zen text-zen-ink dark:text-zen-paper text-sm">Tip: No Audio? Try saying Hello to begin your training</h3>}
                  </div>
                  <button
                    onClick={() => setDarkMode(prev => !prev)}
                    className="bg-zen-ink dark:bg-zen-purple text-zen-paper px-4 py-2 rounded-zen hover:bg-zen-red dark:hover:bg-zen-accent transition-all"
                  >
                    {darkMode ? '☀️ Light' : '🌙 Dark'}
                  </button>
                </div>
                <section
                  className="flex-1 m-4 rounded-zen overflow-hidden shadow-zen"
                  ref={editorContainerRef}
                >
                  <AceEditor
                    mode={getEditorMode(selectedLanguage)}
                    theme={getEditorTheme(darkMode)}
                    value={code}
                    onChange={setCode}
                    name="code-editor"
                    width="100%"
                    height="100%"
                    className="rounded-zen"
                    editorProps={{ $blockScrolling: true }}
                    setOptions={getAceEditorOptions(darkMode)}
                    style={{
                      fontFamily: '"Fira Code", "Fira Mono", monospace',
                      borderRadius: '4px',
                      transition: 'all 0.3s ease'
                    }}
                  />
                </section>
                <section className="h-32 p-4 bg-zen-paper/80 dark:bg-zen-ink/80 backdrop-blur-sm">
                  <SessionControls
                    startSession={startSession}
                    stopSession={stopSession}
                    sendClientEvent={sendClientEvent}
                    sendTextMessage={sendTextMessage}
                    events={events}
                    isSessionActive={isSessionActive}
                    code={code}
                  />
                </section>
              </section>

              {/* Tool panel section - responsive layout */}
              <section className="w-full md:w-[380px] h-[50vh] md:h-full overflow-y-auto bg-zen-paper/90 dark:bg-zen-ink/90 backdrop-blur-sm p-4 pt-0">
                <ToolPanel
                  sendClientEvent={sendClientEvent}
                  sendTextMessage={sendTextMessage}
                  events={events}
                  isSessionActive={isSessionActive}
                  remoteStream={remoteStream}
                  onQuestionSelected={setCurrentQuestion}
                />
              </section>
            </div>
          )}
        </main>
      ) : (
        <main className="absolute top-16 left-0 right-0 bottom-0 overflow-y-auto">
          <ProfileTab user={user} />
        </main>
      )}

      {/* Credit Purchase Modal */}
      {showCreditModal && (
        <CreditModal
          isOpen={showCreditModal}
          onClose={() => setShowCreditModal(false)}
          db={db}
          user={user}
          activeTab={activeTab}
        />
      )}

      {/* Navigation Confirmation Modal */}
      <ConfirmationModal
        isOpen={showNavigationModal}
        onConfirm={handleConfirmNavigation}
        onCancel={handleCancelNavigation}
        title="Abandon Your Path?"
        message="The path to mastery requires focus and dedication. Leaving now will cause your current training to be lost. Are you certain you wish to depart?"
      />

      <style jsx global>{`
        /* Zen-inspired scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        ::-webkit-scrollbar-track {
          background: transparent;
        }

        ::-webkit-scrollbar-thumb {
          background: var(--color-accent);
          border-radius: 4px;
        }

        .dark-theme ::-webkit-scrollbar-thumb {
          background: var(--color-dark-accent);
        }

        ::-webkit-scrollbar-thumb:hover {
          background: var(--color-highlight);
        }

        /* Ace Editor Customizations */
        .ace_gutter {
          background-color: var(--color-base) !important;
          color: var(--color-ink) !important;
          border-right: 1px solid var(--color-accent) !important;
          transition: all 0.3s ease;
        }

        .dark-theme .ace_gutter {
          background-color: var(--color-dark-base) !important;
          color: var(--color-dark-text) !important;
          border-right: 1px solid var(--color-dark-accent) !important;
        }

        .ace_editor {
          font-family: "Fira Code", "Fira Mono", monospace !important;
          line-height: 1.5 !important;
        }

        .ace_cursor {
          color: var(--color-highlight) !important;
          width: 2px !important;
        }

        .dark-theme .ace_cursor {
          color: var(--color-dark-accent) !important;
        }

        /* Smooth animations */
        .ace_editor, .ace_scroller, .ace_content {
          transition: background-color 0.3s ease;
        }

        /* Language selector styling */
        optgroup {
          font-family: "Noto Serif JP", serif;
          font-weight: 500;
          color: var(--color-highlight);
        }

        .dark-theme optgroup {
          color: var(--color-dark-accent);
        }

        option {
          font-family: "Fira Code", "Fira Mono", monospace;
          padding: 4px;
        }
      `}</style>
    </>
  )
}
